const { GraphQLError } = require('graphql');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

process.env.JWT_SECRET = 'test-secret';
const { resolvers } = require('../resolvers');

describe('login mutation', () => {
  const agent = {
    hiveId: '1',
    username: 'alice',
    role: 'ADMIN',
    backingPerson: [{ hiveId: '2', firstName: 'John', lastName: 'Doe' }],
  };

  test('successful login sets cookies and returns payload', async () => {
    const session = {
      run: jest.fn().mockResolvedValue({
        records: [{ get: (k) => (k === 'agent' ? agent : 'hash') }],
      }),
      close: jest.fn(),
    };
    const driver = { session: jest.fn(() => session) };
    const res = { cookie: jest.fn(), set: jest.fn() };
    jest.spyOn(bcrypt, 'compare').mockResolvedValue(true);
    jest.spyOn(jwt, 'sign').mockReturnValue('signed');
    jest.spyOn(crypto, 'randomBytes').mockReturnValue(Buffer.from('csrf'));

    const result = await resolvers.Mutation.login(
      null,
      { username: 'alice', password: 'secret' },
      { driver, res }
    );

    expect(res.cookie).toHaveBeenCalledWith(
      'agentAuth',
      'signed',
      expect.objectContaining({ httpOnly: true })
    );
    expect(res.cookie).toHaveBeenCalledWith(
      'agentData',
      JSON.stringify(agent),
      expect.any(Object)
    );
    expect(res.set).toHaveBeenCalledWith('x-csrf-token', expect.any(String));
    expect(result).toEqual({ success: true, agent });
  });

  test('throws GraphQLError for invalid credentials', async () => {
    const session = {
      run: jest.fn().mockResolvedValue({ records: [] }),
      close: jest.fn(),
    };
    const driver = { session: jest.fn(() => session) };
    const res = { cookie: jest.fn(), set: jest.fn() };

    try {
      await resolvers.Mutation.login(
        null,
        { username: 'bob', password: 'wrong' },
        { driver, res }
      );
    } catch (err) {
      expect(err).toBeInstanceOf(GraphQLError);
      expect(err.extensions.code).toBe('UNAUTHENTICATED');
    }
  });
});
